'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { formatDate } from '@/lib/utils'
import { Search, Plus, Calendar, Users, BookOpen, User, Clock, CheckCircle, Edit, Trash2, Loader2, UserCheck } from 'lucide-react'
import ClassForm from '@/components/forms/class-form'
import AttendanceForm from '@/components/forms/attendance-form'

interface Class {
  id: string
  groupId: string
  teacherId: string
  date: string
  topic: string | null
  homework: string | null
  notes: string | null
  createdAt: string
  group: {
    id: string
    name: string
    course: {
      name: string
      level: string
    }
    _count: {
      enrollments: number
    }
  }
  teacher: {
    user: {
      id: string
      name: string
      phone: string
    }
  }
  attendanceStats?: {
    totalStudents: number
    presentStudents: number
    attendanceRate: number
  }
  _count: {
    attendances: number
  }
}

export default function ClassesPage() {
  const [classes, setClasses] = useState<Class[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [dateFilter, setDateFilter] = useState('')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isAttendanceDialogOpen, setIsAttendanceDialogOpen] = useState(false)
  const [editingClass, setEditingClass] = useState<Class | null>(null)
  const [attendanceClass, setAttendanceClass] = useState<Class | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchClasses = useCallback(async () => {
    try {
      setLoading(true)
      let url = '/api/classes?limit=50'

      if (dateFilter) {
        url += `&dateFrom=${dateFilter}&dateTo=${dateFilter}`
      }

      const response = await fetch(url)
      const data = await response.json()
      setClasses(data.classes || [])
      setError(null)
    } catch (error) {
      console.error('Error fetching classes:', error)
      setError('Failed to fetch classes')
    } finally {
      setLoading(false)
    }
  }, [dateFilter])

  // Handle class creation
  const handleCreateClass = async (data: any) => {
    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch('/api/classes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create class')
      }

      setIsCreateDialogOpen(false)
      fetchClasses() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle class update
  const handleUpdateClass = async (data: any) => {
    if (!editingClass) return

    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch(`/api/classes/${editingClass.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update class')
      }

      setIsEditDialogOpen(false)
      setEditingClass(null)
      fetchClasses() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle class deletion
  const handleDeleteClass = async (classId: string) => {
    if (!confirm('Are you sure you want to delete this class? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/classes/${classId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete class')
      }

      fetchClasses() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    }
  }

  // Handle attendance submission
  const handleAttendanceSubmit = async (data: any) => {
    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch('/api/attendance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to save attendance')
      }

      setIsAttendanceDialogOpen(false)
      setAttendanceClass(null)
      fetchClasses() // Refresh to update attendance stats
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  useEffect(() => {
    fetchClasses()
  }, [fetchClasses])

  const filteredClasses = classes.filter(classItem =>
    classItem.group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    classItem.group.course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    classItem.teacher.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    classItem.topic?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getAttendanceColor = (rate: number) => {
    if (rate >= 80) return 'text-green-600'
    if (rate >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getDateColor = (date: string) => {
    const classDate = new Date(date)
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    if (classDate.toDateString() === today.toDateString()) {
      return 'bg-blue-100 text-blue-800'
    } else if (classDate.toDateString() === tomorrow.toDateString()) {
      return 'bg-green-100 text-green-800'
    } else if (classDate < today) {
      return 'bg-gray-100 text-gray-800'
    } else {
      return 'bg-purple-100 text-purple-800'
    }
  }

  const getDateLabel = (date: string) => {
    const classDate = new Date(date)
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    if (classDate.toDateString() === today.toDateString()) {
      return 'Today'
    } else if (classDate.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow'
    } else if (classDate < today) {
      return 'Past'
    } else {
      return 'Upcoming'
    }
  }

  // Calculate statistics
  const totalClasses = classes.length
  const todayClasses = classes.filter(c => {
    const classDate = new Date(c.date)
    const today = new Date()
    return classDate.toDateString() === today.toDateString()
  }).length
  
  const upcomingClasses = classes.filter(c => {
    const classDate = new Date(c.date)
    const today = new Date()
    return classDate > today
  }).length

  const totalAttendances = classes.reduce((sum, c) => sum + c._count.attendances, 0)
  const totalStudents = classes.reduce((sum, c) => sum + c.group._count.enrollments, 0)
  const overallAttendanceRate = totalStudents > 0 ? (totalAttendances / totalStudents) * 100 : 0

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading classes...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Class Scheduling</h1>
          <p className="text-gray-600">Manage class schedules and sessions</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Schedule Class
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Schedule New Class</DialogTitle>
              <DialogDescription>
                Create a new class session for a group.
              </DialogDescription>
            </DialogHeader>
            <ClassForm
              onSubmit={handleCreateClass}
              onCancel={() => setIsCreateDialogOpen(false)}
              isEditing={false}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by group, course, teacher, or topic..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Input
              type="date"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              placeholder="Filter by date"
            />
          </div>
        </CardContent>
      </Card>

      {/* Class Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Classes</p>
                <p className="text-2xl font-bold text-gray-900">{totalClasses}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Today&apos;s Classes</p>
                <p className="text-2xl font-bold text-gray-900">{todayClasses}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Upcoming</p>
                <p className="text-2xl font-bold text-gray-900">{upcomingClasses}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Attendance Rate</p>
                <p className="text-2xl font-bold text-gray-900">{overallAttendanceRate.toFixed(1)}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Classes Table */}
      <Card>
        <CardHeader>
          <CardTitle>Classes ({filteredClasses.length})</CardTitle>
          <CardDescription>
            Scheduled classes and their details
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date & Time</TableHead>
                <TableHead>Group</TableHead>
                <TableHead>Course</TableHead>
                <TableHead>Teacher</TableHead>
                <TableHead>Topic</TableHead>
                <TableHead>Students</TableHead>
                <TableHead>Attendance</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredClasses.map((classItem) => (
                <TableRow key={classItem.id}>
                  <TableCell>
                    <div>
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <span className="text-sm font-medium">
                          {formatDate(classItem.date)}
                        </span>
                      </div>
                      <Badge className={getDateColor(classItem.date)} size="sm">
                        {getDateLabel(classItem.date)}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-2 text-blue-600" />
                      <span className="font-medium">{classItem.group.name}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {classItem.group.course.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        Level: {classItem.group.course.level}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-2 text-gray-400" />
                      <div>
                        <div className="text-sm font-medium">
                          {classItem.teacher.user.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {classItem.teacher.user.phone}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <BookOpen className="h-4 w-4 mr-2 text-green-600" />
                      <span className="text-sm">
                        {classItem.topic || 'No topic set'}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-1 text-blue-600" />
                      <span className="font-medium">{classItem.group._count.enrollments}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {classItem.attendanceStats ? (
                      <div>
                        <div className="text-sm font-medium">
                          {classItem.attendanceStats.presentStudents}/{classItem.attendanceStats.totalStudents}
                        </div>
                        <div className={`text-sm ${getAttendanceColor(classItem.attendanceStats.attendanceRate)}`}>
                          {classItem.attendanceStats.attendanceRate.toFixed(1)}%
                        </div>
                      </div>
                    ) : (
                      <div className="text-sm text-gray-500">
                        {classItem._count.attendances > 0 ? 'Marked' : 'Not marked'}
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setEditingClass(classItem)
                          setIsEditDialogOpen(true)
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setAttendanceClass(classItem)
                          setIsAttendanceDialogOpen(true)
                        }}
                      >
                        <UserCheck className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteClass(classItem.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Edit Class Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Edit Class</DialogTitle>
            <DialogDescription>
              Update class information and details.
            </DialogDescription>
          </DialogHeader>
          {editingClass && (
            <ClassForm
              initialData={{
                groupId: editingClass.groupId,
                teacherId: editingClass.teacherId,
                date: editingClass.date,
                topic: editingClass.topic || '',
                homework: editingClass.homework || '',
                notes: editingClass.notes || ''
              }}
              onSubmit={handleUpdateClass}
              onCancel={() => {
                setIsEditDialogOpen(false)
                setEditingClass(null)
              }}
              isEditing={true}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Attendance Dialog */}
      <Dialog open={isAttendanceDialogOpen} onOpenChange={setIsAttendanceDialogOpen}>
        <DialogContent className="max-w-5xl">
          <DialogHeader>
            <DialogTitle>Mark Attendance</DialogTitle>
            <DialogDescription>
              Mark attendance for students in this class.
            </DialogDescription>
          </DialogHeader>
          {attendanceClass && (
            <AttendanceForm
              preselectedClassId={attendanceClass.id}
              onSubmit={handleAttendanceSubmit}
              onCancel={() => {
                setIsAttendanceDialogOpen(false)
                setAttendanceClass(null)
              }}
              isEditing={false}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
