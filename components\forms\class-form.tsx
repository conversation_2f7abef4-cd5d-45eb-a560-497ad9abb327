'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Calendar, Users, BookOpen, User } from 'lucide-react'

const classSchema = z.object({
  groupId: z.string().min(1, 'Group is required'),
  teacherId: z.string().min(1, 'Teacher is required'),
  date: z.string().min(1, 'Date is required'),
  time: z.string().min(1, 'Time is required'),
  topic: z.string().optional(),
  homework: z.string().optional(),
  notes: z.string().optional(),
})

type ClassFormData = z.infer<typeof classSchema>

interface ClassFormProps {
  initialData?: Partial<ClassFormData & { id?: string }>
  onSubmit: (data: ClassFormData) => Promise<void>
  onCancel?: () => void
  isEditing?: boolean
}

interface Group {
  id: string
  name: string
  course: {
    name: string
    level: string
  }
  teacher: {
    id: string
    user: {
      name: string
    }
  }
  _count: {
    enrollments: number
  }
}

interface Teacher {
  id: string
  user: {
    name: string
    phone: string
  }
  subject: string
}

function ClassForm({ initialData, onSubmit, onCancel, isEditing = false }: ClassFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [groups, setGroups] = useState<Group[]>([])
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null)

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<ClassFormData>({
    resolver: zodResolver(classSchema),
    defaultValues: {
      groupId: initialData?.groupId || '',
      teacherId: initialData?.teacherId || '',
      date: initialData?.date ? new Date(initialData.date).toISOString().split('T')[0] : '',
      time: initialData?.date ? new Date(initialData.date).toTimeString().slice(0, 5) : '',
      topic: initialData?.topic || '',
      homework: initialData?.homework || '',
      notes: initialData?.notes || '',
    },
  })

  const selectedGroupId = watch('groupId')

  useEffect(() => {
    fetchGroups()
    fetchTeachers()
  }, [])

  useEffect(() => {
    if (selectedGroupId) {
      const group = groups.find(g => g.id === selectedGroupId)
      setSelectedGroup(group || null)
      
      // Auto-select the group's teacher if available
      if (group && !isEditing) {
        setValue('teacherId', group.teacher.id)
      }
    }
  }, [selectedGroupId, groups, setValue, isEditing])

  const fetchGroups = async () => {
    try {
      const response = await fetch('/api/groups?active=true')
      const data = await response.json()
      setGroups(data.groups || [])
    } catch (error) {
      console.error('Error fetching groups:', error)
    }
  }

  const fetchTeachers = async () => {
    try {
      const response = await fetch('/api/teachers')
      const data = await response.json()
      setTeachers(data.teachers || [])
    } catch (error) {
      console.error('Error fetching teachers:', error)
    }
  }

  const handleFormSubmit = async (data: ClassFormData) => {
    setIsSubmitting(true)
    setError(null)

    try {
      // Combine date and time into a single datetime string
      const datetime = new Date(`${data.date}T${data.time}:00`)
      const formattedData = {
        ...data,
        date: datetime.toISOString(),
      }
      
      await onSubmit(formattedData)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Calendar className="h-5 w-5 mr-2" />
          {isEditing ? 'Edit Class' : 'Schedule New Class'}
        </CardTitle>
        <CardDescription>
          {isEditing ? 'Update class information' : 'Schedule a new class session for a group'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Group Selection */}
          <div className="space-y-2">
            <Label htmlFor="groupId">Group *</Label>
            <Select
              value={watch('groupId')}
              onValueChange={(value) => setValue('groupId', value)}
            >
              <SelectTrigger className={errors.groupId ? 'border-red-500' : ''}>
                <SelectValue placeholder="Select group" />
              </SelectTrigger>
              <SelectContent>
                {groups.map((group) => (
                  <SelectItem key={group.id} value={group.id}>
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4" />
                      <span>{group.name} - {group.course.name} ({group._count.enrollments} students)</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.groupId && (
              <p className="text-sm text-red-500">{errors.groupId.message}</p>
            )}

            {selectedGroup && (
              <div className="bg-blue-50 p-3 rounded-lg">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium">{selectedGroup.name}</p>
                    <p className="text-sm text-gray-600">{selectedGroup.course.name} - {selectedGroup.course.level}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-600">{selectedGroup._count.enrollments} students</p>
                    <p className="text-sm text-gray-600">Teacher: {selectedGroup.teacher.user.name}</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Teacher Selection */}
          <div className="space-y-2">
            <Label htmlFor="teacherId">Teacher *</Label>
            <Select
              value={watch('teacherId')}
              onValueChange={(value) => setValue('teacherId', value)}
            >
              <SelectTrigger className={errors.teacherId ? 'border-red-500' : ''}>
                <SelectValue placeholder="Select teacher" />
              </SelectTrigger>
              <SelectContent>
                {teachers.map((teacher) => (
                  <SelectItem key={teacher.id} value={teacher.id}>
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4" />
                      <span>{teacher.user.name} - {teacher.subject}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.teacherId && (
              <p className="text-sm text-red-500">{errors.teacherId.message}</p>
            )}
          </div>

          {/* Date and Time */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="date">Date *</Label>
              <Input
                id="date"
                type="date"
                {...register('date')}
                className={errors.date ? 'border-red-500' : ''}
              />
              {errors.date && (
                <p className="text-sm text-red-500">{errors.date.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="time">Time *</Label>
              <Input
                id="time"
                type="time"
                {...register('time')}
                className={errors.time ? 'border-red-500' : ''}
              />
              {errors.time && (
                <p className="text-sm text-red-500">{errors.time.message}</p>
              )}
            </div>
          </div>

          {/* Topic */}
          <div className="space-y-2">
            <Label htmlFor="topic">Topic</Label>
            <Input
              id="topic"
              placeholder="Enter class topic (optional)"
              {...register('topic')}
            />
          </div>

          {/* Homework */}
          <div className="space-y-2">
            <Label htmlFor="homework">Homework</Label>
            <Textarea
              id="homework"
              placeholder="Enter homework assignment (optional)"
              rows={3}
              {...register('homework')}
            />
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              placeholder="Enter additional notes (optional)"
              rows={3}
              {...register('notes')}
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? 'Update Class' : 'Schedule Class'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

export default ClassForm
export { ClassForm }
