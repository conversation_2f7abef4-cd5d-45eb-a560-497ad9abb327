import { Sidebar } from '@/components/dashboard/sidebar'
import { <PERSON><PERSON> } from '@/components/dashboard/header'
import { Toaster } from '@/components/ui/toaster'
import { BranchProvider } from '@/contexts/branch-context'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <BranchProvider>
      <div className="flex h-screen bg-gray-100">
        <Sidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Header />
          <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
            {children}
          </main>
        </div>
        <Toaster />
      </div>
    </BranchProvider>
  )
}
