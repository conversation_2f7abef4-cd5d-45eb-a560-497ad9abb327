import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import * as z from 'zod'

const classSchema = z.object({
  groupId: z.string(),
  teacherId: z.string(),
  date: z.string(),
  topic: z.string().optional(),
  homework: z.string().optional(),
  notes: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const groupId = searchParams.get('groupId')
    const teacherId = searchParams.get('teacherId')
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')
    const search = searchParams.get('search')

    const where: any = {}

    if (groupId) {
      where.groupId = groupId
    }

    if (teacherId) {
      where.teacherId = teacherId
    }

    if (dateFrom || dateTo) {
      where.date = {
        ...(dateFrom && { gte: new Date(dateFrom) }),
        ...(dateTo && { lte: new Date(dateTo) }),
      }
    }

    if (search) {
      where.OR = [
        { topic: { contains: search, mode: 'insensitive' } },
        { homework: { contains: search, mode: 'insensitive' } },
        { notes: { contains: search, mode: 'insensitive' } },
        { group: { name: { contains: search, mode: 'insensitive' } } },
        { teacher: { user: { name: { contains: search, mode: 'insensitive' } } } },
      ]
    }

    const [classes, total] = await Promise.all([
      prisma.class.findMany({
        where,
        include: {
          group: {
            include: {
              course: {
                select: {
                  name: true,
                  level: true,
                },
              },
              _count: {
                select: {
                  enrollments: {
                    where: { status: 'ACTIVE' },
                  },
                },
              },
            },
          },
          teacher: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  phone: true,
                },
              },
            },
          },
          attendances: {
            include: {
              student: {
                include: {
                  user: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
          _count: {
            select: {
              attendances: true,
            },
          },
        },
        orderBy: { date: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.class.count({ where }),
    ])

    // Calculate attendance statistics for each class
    const classesWithStats = classes.map(cls => {
      const totalStudents = cls.group._count.enrollments
      const presentStudents = cls.attendances.filter(att => att.status === 'PRESENT').length
      const attendanceRate = totalStudents > 0 ? (presentStudents / totalStudents) * 100 : 0

      return {
        ...cls,
        attendanceStats: {
          totalStudents,
          presentStudents,
          attendanceRate: Math.round(attendanceRate * 100) / 100,
        },
      }
    })

    return NextResponse.json({
      classes: classesWithStats,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching classes:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = classSchema.parse(body)

    // Check if group exists and is active
    const group = await prisma.group.findUnique({
      where: { id: validatedData.groupId },
      include: {
        course: true,
        enrollments: {
          where: { status: 'ACTIVE' },
        },
      },
    })

    if (!group) {
      return NextResponse.json(
        { error: 'Group not found' },
        { status: 400 }
      )
    }

    if (!group.isActive) {
      return NextResponse.json(
        { error: 'Cannot create class for inactive group' },
        { status: 400 }
      )
    }

    // Check if teacher exists
    const teacher = await prisma.teacher.findUnique({
      where: { id: validatedData.teacherId },
    })

    if (!teacher) {
      return NextResponse.json(
        { error: 'Teacher not found' },
        { status: 400 }
      )
    }

    // Check if class already exists for this group on this date
    const existingClass = await prisma.class.findFirst({
      where: {
        groupId: validatedData.groupId,
        date: new Date(validatedData.date),
      },
    })

    if (existingClass) {
      return NextResponse.json(
        { error: 'Class already exists for this group on this date' },
        { status: 400 }
      )
    }

    const newClass = await prisma.class.create({
      data: {
        ...validatedData,
        date: new Date(validatedData.date),
      },
      include: {
        group: {
          include: {
            course: {
              select: {
                name: true,
                level: true,
              },
            },
          },
        },
        teacher: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        _count: {
          select: {
            attendances: true,
          },
        },
      },
    })

    return NextResponse.json(newClass, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating class:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
