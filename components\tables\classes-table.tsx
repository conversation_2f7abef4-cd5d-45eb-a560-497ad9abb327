"use client"

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Search, Plus, Edit, Trash2, Download, Filter, Calendar, Clock, Users } from "lucide-react"

interface Class {
  id: string
  group: {
    name: string
    course: {
      name: string
      level: string
    }
    capacity: number
    room: string | null
    branch: string
    schedule: string
    enrollments: Array<{
      student: {
        user: {
          name: string
        }
      }
    }>
  }
  teacher: {
    user: {
      name: string
      phone: string
    }
    subject: string
  }
  date: string
  topic: string | null
  homework: string | null
  notes: string | null
  attendances: Array<{
    status: string
  }>
  createdAt: string
  updatedAt: string
}

interface ClassesTableProps {
  initialData?: Class[]
}

export function ClassesTable({ initialData = [] }: ClassesTableProps) {
  const [classes, setClasses] = useState<Class[]>(initialData)
  const [filteredClasses, setFilteredClasses] = useState<Class[]>(initialData)
  const [searchTerm, setSearchTerm] = useState("")
  const [groupFilter, setGroupFilter] = useState("")
  const [teacherFilter, setTeacherFilter] = useState("")
  const [branchFilter, setBranchFilter] = useState("")
  const [dateFilter, setDateFilter] = useState("")
  const [loading, setLoading] = useState(false)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingClass, setEditingClass] = useState<Class | null>(null)

  // Fetch classes data
  const fetchClasses = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/classes")
      if (response.ok) {
        const data = await response.json()
        setClasses(data)
        setFilteredClasses(data)
      }
    } catch (error) {
      console.error("Error fetching classes:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (initialData.length === 0) {
      fetchClasses()
    }
  }, [initialData])

  // Filter classes based on search and filters
  useEffect(() => {
    let filtered = classes

    if (searchTerm) {
      filtered = filtered.filter(
        (classItem) =>
          classItem.group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          classItem.group.course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          classItem.teacher.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          classItem.topic?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (groupFilter) {
      filtered = filtered.filter((classItem) => classItem.group.name === groupFilter)
    }

    if (teacherFilter) {
      filtered = filtered.filter((classItem) => classItem.teacher.user.name === teacherFilter)
    }

    if (branchFilter) {
      filtered = filtered.filter((classItem) => classItem.group.branch === branchFilter)
    }

    if (dateFilter) {
      const filterDate = new Date(dateFilter)
      filtered = filtered.filter((classItem) => {
        const classDate = new Date(classItem.date)
        return classDate.toDateString() === filterDate.toDateString()
      })
    }

    setFilteredClasses(filtered)
  }, [classes, searchTerm, groupFilter, teacherFilter, branchFilter, dateFilter])

  // Get unique values for filters
  const uniqueGroups = [...new Set(classes.map((classItem) => classItem.group.name))]
  const uniqueTeachers = [...new Set(classes.map((classItem) => classItem.teacher.user.name))]
  const uniqueBranches = [...new Set(classes.map((classItem) => classItem.group.branch))]

  // Handle class deletion
  const handleDelete = async (classId: string) => {
    if (!confirm("Are you sure you want to delete this class?")) return

    try {
      const response = await fetch(`/api/classes/${classId}`, {
        method: "DELETE",
      })

      if (response.ok) {
        setClasses(classes.filter((classItem) => classItem.id !== classId))
      } else {
        alert("Failed to delete class")
      }
    } catch (error) {
      console.error("Error deleting class:", error)
      alert("Error deleting class")
    }
  }

  // Handle form submission
  const handleFormSubmit = () => {
    setIsCreateDialogOpen(false)
    setEditingClass(null)
    fetchClasses()
  }

  // Export to CSV
  const exportToCSV = () => {
    const headers = [
      "Group",
      "Course",
      "Teacher",
      "Branch",
      "Date",
      "Topic",
      "Room",
      "Students Enrolled",
      "Attendance Rate",
      "Homework",
      "Notes",
    ]
    const csvData = filteredClasses.map((classItem) => {
      const attendanceRate = classItem.attendances.length > 0
        ? ((classItem.attendances.filter(a => a.status === "PRESENT").length / classItem.attendances.length) * 100).toFixed(1)
        : "0"
      
      return [
        classItem.group.name,
        classItem.group.course.name,
        classItem.teacher.user.name,
        classItem.group.branch,
        new Date(classItem.date).toLocaleDateString(),
        classItem.topic || "",
        classItem.group.room || "",
        classItem.group.enrollments.length,
        `${attendanceRate}%`,
        classItem.homework || "",
        classItem.notes || "",
      ]
    })

    const csvContent = [headers, ...csvData]
      .map((row) => row.map((field) => `"${field}"`).join(","))
      .join("\n")

    const blob = new Blob([csvContent], { type: "text/csv" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `classes-${new Date().toISOString().split("T")[0]}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }

  // Calculate statistics
  const totalClasses = filteredClasses.length
  const todayClasses = filteredClasses.filter((classItem) => {
    const today = new Date()
    const classDate = new Date(classItem.date)
    return classDate.toDateString() === today.toDateString()
  }).length

  const upcomingClasses = filteredClasses.filter((classItem) => {
    const today = new Date()
    const classDate = new Date(classItem.date)
    return classDate > today
  }).length

  const totalStudents = filteredClasses.reduce((sum, classItem) => sum + classItem.group.enrollments.length, 0)

  return (
    <div className="space-y-4">
      {/* Header with actions */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <h2 className="text-2xl font-bold">Class Schedule</h2>
        <div className="flex gap-2">
          <Button onClick={exportToCSV} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Schedule Class
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Schedule New Class</DialogTitle>
              </DialogHeader>
              <div className="p-4">
                <p className="text-sm text-gray-500">
                  Class scheduling form would be implemented here
                </p>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-blue-600">Total Classes</h3>
          <p className="text-2xl font-bold text-blue-900">{totalClasses}</p>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-green-600">Today&apos;s Classes</h3>
          <p className="text-2xl font-bold text-green-900">{todayClasses}</p>
        </div>
        <div className="bg-purple-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-purple-600">Upcoming</h3>
          <p className="text-2xl font-bold text-purple-900">{upcomingClasses}</p>
        </div>
        <div className="bg-orange-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-orange-600">Total Students</h3>
          <p className="text-2xl font-bold text-orange-900">{totalStudents}</p>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search classes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <select
          value={groupFilter}
          onChange={(e) => setGroupFilter(e.target.value)}
          className="px-3 py-2 border rounded-md"
        >
          <option value="">All Groups</option>
          {uniqueGroups.map((group) => (
            <option key={group} value={group}>
              {group}
            </option>
          ))}
        </select>
        <select
          value={teacherFilter}
          onChange={(e) => setTeacherFilter(e.target.value)}
          className="px-3 py-2 border rounded-md"
        >
          <option value="">All Teachers</option>
          {uniqueTeachers.map((teacher) => (
            <option key={teacher} value={teacher}>
              {teacher}
            </option>
          ))}
        </select>
        <select
          value={branchFilter}
          onChange={(e) => setBranchFilter(e.target.value)}
          className="px-3 py-2 border rounded-md"
        >
          <option value="">All Branches</option>
          {uniqueBranches.map((branch) => (
            <option key={branch} value={branch}>
              {branch}
            </option>
          ))}
        </select>
        <Input
          type="date"
          value={dateFilter}
          onChange={(e) => setDateFilter(e.target.value)}
          className="w-auto"
        />
        {(groupFilter || teacherFilter || branchFilter || dateFilter || searchTerm) && (
          <Button
            variant="outline"
            onClick={() => {
              setGroupFilter("")
              setTeacherFilter("")
              setBranchFilter("")
              setDateFilter("")
              setSearchTerm("")
            }}
          >
            Clear Filters
          </Button>
        )}
      </div>

      {/* Table */}
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date & Time</TableHead>
              <TableHead>Group</TableHead>
              <TableHead>Course</TableHead>
              <TableHead>Teacher</TableHead>
              <TableHead>Room</TableHead>
              <TableHead>Students</TableHead>
              <TableHead>Topic</TableHead>
              <TableHead>Attendance</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8">
                  Loading classes...
                </TableCell>
              </TableRow>
            ) : filteredClasses.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8">
                  No classes found
                </TableCell>
              </TableRow>
            ) : (
              filteredClasses.map((classItem) => {
                const attendanceRate = classItem.attendances.length > 0
                  ? ((classItem.attendances.filter(a => a.status === "PRESENT").length / classItem.attendances.length) * 100).toFixed(1)
                  : "0"
                
                const isToday = new Date(classItem.date).toDateString() === new Date().toDateString()
                const isPast = new Date(classItem.date) < new Date()

                return (
                  <TableRow key={classItem.id} className={isToday ? "bg-blue-50" : ""}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className={`font-medium ${isToday ? "text-blue-600" : ""}`}>
                          {new Date(classItem.date).toLocaleDateString()}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {new Date(classItem.date).toLocaleTimeString([], { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })}
                        </div>
                        {isToday && (
                          <Badge variant="default" className="text-xs">
                            Today
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <Badge variant="outline">{classItem.group.name}</Badge>
                        <div className="text-sm text-gray-500 mt-1">
                          {classItem.group.branch}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{classItem.group.course.name}</div>
                        <div className="text-sm text-gray-500">
                          Level: {classItem.group.course.level}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{classItem.teacher.user.name}</div>
                        <div className="text-sm text-gray-500">
                          {classItem.teacher.subject}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {classItem.group.room ? (
                        <Badge variant="secondary">{classItem.group.room}</Badge>
                      ) : (
                        <span className="text-gray-400">Not assigned</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Users className="h-4 w-4 text-gray-400" />
                        <span>{classItem.group.enrollments.length}</span>
                        <span className="text-gray-400">/ {classItem.group.capacity}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {classItem.topic ? (
                        <div className="max-w-32 truncate" title={classItem.topic}>
                          {classItem.topic}
                        </div>
                      ) : (
                        <span className="text-gray-400">No topic</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {classItem.attendances.length > 0 ? (
                        <div className="space-y-1">
                          <div className="text-sm font-medium">{attendanceRate}%</div>
                          <div className="text-xs text-gray-500">
                            {classItem.attendances.filter(a => a.status === "PRESENT").length} / {classItem.attendances.length}
                          </div>
                        </div>
                      ) : (
                        <span className="text-gray-400">No data</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setEditingClass(classItem)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>Edit Class</DialogTitle>
                            </DialogHeader>
                            <div className="p-4">
                              <p className="text-sm text-gray-500">
                                Class editing form would be implemented here
                              </p>
                            </div>
                          </DialogContent>
                        </Dialog>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(classItem.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                )
              })
            )}
          </TableBody>
        </Table>
      </div>

      {/* Results count */}
      <div className="text-sm text-gray-500">
        Showing {filteredClasses.length} of {classes.length} classes
      </div>
    </div>
  )
}
