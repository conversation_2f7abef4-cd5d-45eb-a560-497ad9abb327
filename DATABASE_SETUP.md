# Database Setup Guide

## Quick Start

### Option 1: Using Docker (Recommended for Development)

1. **Install Docker Desktop** if not already installed

2. **Run PostgreSQL container:**
```bash
docker run --name inno-crm-postgres \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=password \
  -e POSTGRES_DB=inno_crm_dev \
  -p 5432:5432 \
  -d postgres:15
```

3. **Update your `.env` file:**
```env
DATABASE_URL="postgresql://postgres:password@localhost:5432/inno_crm_dev"
```

4. **Run the migration script:**
```bash
node scripts/migrate-database.js
```

### Option 2: Local PostgreSQL Installation

1. **Install PostgreSQL** on your system
2. **Create a database:**
```sql
CREATE DATABASE inno_crm_dev;
CREATE USER your_username WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE inno_crm_dev TO your_username;
```

3. **Update your `.env` file:**
```env
DATABASE_URL="postgresql://your_username:your_password@localhost:5432/inno_crm_dev"
```

4. **Run the migration script:**
```bash
node scripts/migrate-database.js
```

## Manual Migration (Alternative)

If the script doesn't work, you can run these commands manually:

```bash
# Generate Prisma client
npx prisma generate

# Push schema to database
npx prisma db push

# Optional: Seed the database
npx prisma db seed
```

## Troubleshooting

### Common Issues:

1. **Authentication failed (P1000)**
   - Check your username/password in DATABASE_URL
   - Ensure PostgreSQL is running
   - Verify the database exists

2. **Connection refused**
   - Make sure PostgreSQL is running on port 5432
   - Check if another service is using port 5432

3. **Database does not exist**
   - Create the database manually using psql or pgAdmin
   - Or use Docker option which creates it automatically

4. **Permission denied**
   - Ensure your user has proper permissions on the database
   - Grant ALL PRIVILEGES to your user

### Verification Commands:

```bash
# Check if PostgreSQL is running
docker ps  # (if using Docker)
# or
pg_isready -h localhost -p 5432

# Test database connection
psql -h localhost -p 5432 -U postgres -d inno_crm_dev

# Check Prisma connection
npx prisma db pull
```

## Schema Changes Applied

The migration includes these key changes:

1. **Assessment Model Updates:**
   - Added `testName` field for test identification
   - Added `groupId` field for group-based assessments
   - Removed `templateId` and related template fields
   - Simplified assessment workflow

2. **Removed Models:**
   - `TestTemplate` model completely removed
   - Related API endpoints cleaned up

3. **Updated Enums:**
   - Simplified `AssessmentType` enum
   - Removed complex test types

## Next Steps

After successful migration:

1. **Start the development server:**
```bash
npm run dev
```

2. **Access the application:**
   - Open http://localhost:3001
   - Test with different user roles

3. **Test new features:**
   - Admin-only financial access
   - Cashier role limitations
   - Simplified assessment recording

## Production Deployment

For production, ensure:

1. Use a proper PostgreSQL instance (not Docker for production)
2. Set strong passwords and proper security
3. Use environment variables for sensitive data
4. Run migrations in a maintenance window
5. Backup your database before migration
