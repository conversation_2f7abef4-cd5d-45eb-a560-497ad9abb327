import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  TrendingUp, 
  Calendar, 
  CreditCard, 
  Award, 
  BookOpen, 
  Target,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react'

export default function StudentDashboardPage() {
  // Mock data - in real implementation, this would come from API
  const studentData = {
    name: "<PERSON>",
    level: "B1",
    nextLevel: "B2",
    progress: 65,
    totalClasses: 48,
    attendedClasses: 42,
    upcomingClasses: 3,
    completedAssignments: 15,
    pendingAssignments: 2,
    totalPayments: 2400000,
    paidAmount: 1800000,
    pendingAmount: 600000
  }

  const attendanceRate = Math.round((studentData.attendedClasses / studentData.totalClasses) * 100)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Student Dashboard</h1>
        <p className="text-gray-600">Welcome back, {studentData.name}!</p>
      </div>

      {/* Progress Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current Level</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Badge className="bg-yellow-100 text-yellow-800">
                Level {studentData.level}
              </Badge>
              <span className="text-xs text-gray-500">→ {studentData.nextLevel}</span>
            </div>
            <div className="mt-2">
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-600">Progress to {studentData.nextLevel}</span>
                <span className="font-medium">{studentData.progress}%</span>
              </div>
              <div className="mt-1 w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full" 
                  style={{ width: `${studentData.progress}%` }}
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Attendance</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{attendanceRate}%</div>
            <p className="text-xs text-muted-foreground">
              {studentData.attendedClasses} of {studentData.totalClasses} classes
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upcoming Classes</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{studentData.upcomingClasses}</div>
            <p className="text-xs text-muted-foreground">
              This week
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Assignments</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{studentData.pendingAssignments}</div>
            <p className="text-xs text-muted-foreground">
              Pending completion
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Access your most used features</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex flex-col space-y-2">
              <Calendar className="h-6 w-6" />
              <span className="text-sm">View Schedule</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col space-y-2">
              <BookOpen className="h-6 w-6" />
              <span className="text-sm">Assignments</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col space-y-2">
              <TrendingUp className="h-6 w-6" />
              <span className="text-sm">Progress Report</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col space-y-2">
              <CreditCard className="h-6 w-6" />
              <span className="text-sm">Payment History</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Recent Classes</CardTitle>
            <CardDescription>Your latest class attendance</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { date: '2024-01-15', topic: 'Present Perfect Tense', status: 'present' },
                { date: '2024-01-13', topic: 'Vocabulary: Travel', status: 'present' },
                { date: '2024-01-11', topic: 'Reading Comprehension', status: 'absent' },
                { date: '2024-01-09', topic: 'Speaking Practice', status: 'present' },
              ].map((classItem, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">{classItem.topic}</p>
                    <p className="text-sm text-gray-500">{classItem.date}</p>
                  </div>
                  <Badge 
                    className={classItem.status === 'present' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                    }
                  >
                    {classItem.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Payment Status</CardTitle>
            <CardDescription>Your payment information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total Amount</span>
                <span className="font-medium">{studentData.totalPayments.toLocaleString()} UZS</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Paid Amount</span>
                <span className="font-medium text-green-600">{studentData.paidAmount.toLocaleString()} UZS</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Pending Amount</span>
                <span className="font-medium text-orange-600">{studentData.pendingAmount.toLocaleString()} UZS</span>
              </div>
              <div className="pt-2 border-t">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-green-500 h-2 rounded-full" 
                    style={{ width: `${(studentData.paidAmount / studentData.totalPayments) * 100}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {Math.round((studentData.paidAmount / studentData.totalPayments) * 100)}% paid
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
