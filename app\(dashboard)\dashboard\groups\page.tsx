'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { formatDate } from '@/lib/utils'
import { Search, Plus, Users, Calendar, MapPin, User, Edit, Trash2, Loader2 } from 'lucide-react'
import GroupForm from '@/components/forms/group-form'

interface Group {
  id: string
  name: string
  capacity: number
  schedule: string
  room?: string
  branch: string
  startDate: string
  endDate: string
  isActive: boolean
  course: {
    name: string
    level: string
  }
  teacher: {
    user: {
      name: string
    }
  }
  enrollments: {
    id: string
    student: {
      user: {
        name: string
      }
    }
    status: string
  }[]
  _count: {
    enrollments: number
  }
}

export default function GroupsPage() {
  const [groups, setGroups] = useState<Group[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingGroup, setEditingGroup] = useState<Group | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchGroups()
  }, [])

  const fetchGroups = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/groups')
      const data = await response.json()
      setGroups(data.groups || [])
      setError(null)
    } catch (error) {
      console.error('Error fetching groups:', error)
      setError('Failed to fetch groups')
    } finally {
      setLoading(false)
    }
  }

  // Handle group creation
  const handleCreateGroup = async (data: any) => {
    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch('/api/groups', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create group')
      }

      setIsCreateDialogOpen(false)
      fetchGroups() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle group update
  const handleUpdateGroup = async (data: any) => {
    if (!editingGroup) return

    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch(`/api/groups/${editingGroup.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update group')
      }

      setIsEditDialogOpen(false)
      setEditingGroup(null)
      fetchGroups() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle group deletion
  const handleDeleteGroup = async (groupId: string) => {
    if (!confirm('Are you sure you want to delete this group? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/groups/${groupId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete group')
      }

      fetchGroups() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    }
  }

  const filteredGroups = groups.filter(group =>
    group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.teacher.user.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
  }

  const getLevelColor = (level: string) => {
    const colors: { [key: string]: string } = {
      'A1': 'bg-red-100 text-red-800',
      'A2': 'bg-orange-100 text-orange-800',
      'B1': 'bg-yellow-100 text-yellow-800',
      'B2': 'bg-green-100 text-green-800',
      'C1': 'bg-blue-100 text-blue-800',
      'C2': 'bg-purple-100 text-purple-800',
      'IELTS_6_0': 'bg-indigo-100 text-indigo-800',
      'IELTS_6_5': 'bg-indigo-100 text-indigo-800',
      'IELTS_7_0': 'bg-indigo-100 text-indigo-800',
      'KIDS': 'bg-pink-100 text-pink-800',
    }
    return colors[level] || 'bg-gray-100 text-gray-800'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading groups...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Groups Management</h1>
          <p className="text-gray-600">Manage class groups and schedules</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Group
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Group</DialogTitle>
              <DialogDescription>
                Set up a new class group with course, teacher, and schedule details.
              </DialogDescription>
            </DialogHeader>
            <GroupForm
              onSubmit={handleCreateGroup}
              onCancel={() => setIsCreateDialogOpen(false)}
              isEditing={false}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle>Search Groups</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search by group name, course, or teacher..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Groups Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredGroups.map((group) => (
          <Card key={group.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{group.name}</CardTitle>
                  <CardDescription className="flex items-center mt-1">
                    <User className="h-3 w-3 mr-1" />
                    {group.teacher.user.name}
                  </CardDescription>
                </div>
                <Badge className={getStatusColor(group.isActive)}>
                  {group.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-700">Course:</p>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">{group.course.name}</span>
                    <Badge className={getLevelColor(group.course.level)}>
                      {group.course.level.replace('_', ' ')}
                    </Badge>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <p className="font-medium text-gray-700">Capacity:</p>
                    <div className="flex items-center">
                      <Users className="h-3 w-3 mr-1 text-gray-500" />
                      <span>{group._count.enrollments}/{group.capacity}</span>
                    </div>
                  </div>
                  <div>
                    <p className="font-medium text-gray-700">Branch:</p>
                    <div className="flex items-center">
                      <MapPin className="h-3 w-3 mr-1 text-gray-500" />
                      <span>{group.branch}</span>
                    </div>
                  </div>
                </div>

                {group.room && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">Room:</p>
                    <p className="text-sm text-gray-600">{group.room}</p>
                  </div>
                )}

                <div>
                  <p className="text-sm font-medium text-gray-700">Schedule:</p>
                  <p className="text-sm text-gray-600">{group.schedule}</p>
                </div>

                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <p className="font-medium text-gray-700">Start Date:</p>
                    <div className="flex items-center">
                      <Calendar className="h-3 w-3 mr-1 text-gray-500" />
                      <span>{formatDate(new Date(group.startDate))}</span>
                    </div>
                  </div>
                  <div>
                    <p className="font-medium text-gray-700">End Date:</p>
                    <div className="flex items-center">
                      <Calendar className="h-3 w-3 mr-1 text-gray-500" />
                      <span>{formatDate(new Date(group.endDate))}</span>
                    </div>
                  </div>
                </div>

                <div className="pt-2">
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => {
                        setEditingGroup(group)
                        setIsEditDialogOpen(true)
                      }}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 text-red-600 hover:text-red-700"
                      onClick={() => handleDeleteGroup(group.id)}
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredGroups.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No groups found matching your search.</p>
        </div>
      )}

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Groups</p>
                <p className="text-2xl font-bold text-gray-900">{groups.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Groups</p>
                <p className="text-2xl font-bold text-gray-900">
                  {groups.filter(g => g.isActive).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Students</p>
                <p className="text-2xl font-bold text-gray-900">
                  {groups.reduce((sum, group) => sum + group._count.enrollments, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg. Group Size</p>
                <p className="text-2xl font-bold text-gray-900">
                  {groups.length > 0 
                    ? Math.round(groups.reduce((sum, group) => sum + group._count.enrollments, 0) / groups.length)
                    : 0
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Edit Group Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Group</DialogTitle>
            <DialogDescription>
              Update group information, schedule, and settings.
            </DialogDescription>
          </DialogHeader>
          {editingGroup && (
            <GroupForm
              initialData={{
                name: editingGroup.name,
                courseId: '', // You'll need to get this from the group data
                teacherId: '', // You'll need to get this from the group data
                capacity: editingGroup.capacity,
                schedule: editingGroup.schedule,
                room: editingGroup.room || '',
                branch: editingGroup.branch,
                startDate: new Date(editingGroup.startDate).toISOString().split('T')[0],
                endDate: new Date(editingGroup.endDate).toISOString().split('T')[0],
                isActive: editingGroup.isActive
              }}
              onSubmit={handleUpdateGroup}
              onCancel={() => {
                setIsEditDialogOpen(false)
                setEditingGroup(null)
              }}
              isEditing={true}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
