'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { cn } from '@/lib/utils'
import {
  LayoutDashboard,
  Users,
  UserPlus,
  GraduationCap,
  BookOpen,
  CreditCard,
  BarChart3,
  Settings,
  Phone,
  UserCheck,
  Calendar,
  ClipboardList,
  ChevronDown,
  ChevronRight,
  Award,
  Target,
  TrendingUp,
  FileText,
  MessageSquare,
  Shield,
  Activity,
  PieChart,
  ClipboardCheck
} from 'lucide-react'
import { useState } from 'react'
import { Badge } from '@/components/ui/badge'

// Define navigation categories and items with role-based access
interface NavigationItem {
  name: string
  href: string
  icon: any
  roles: string[]
  studentLevels?: string[] // For student progression
}

interface NavigationCategory {
  name: string
  items: NavigationItem[]
  roles: string[]
  collapsible?: boolean
}

const navigationConfig: NavigationCategory[] = [
  {
    name: 'Dashboard',
    roles: ['ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION', 'CASHIER', 'STUDENT', 'PARENT'],
    items: [
      {
        name: 'Overview',
        href: '/dashboard',
        icon: LayoutDashboard,
        roles: ['ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION', 'CASHIER', 'STUDENT', 'PARENT']
      },
    ]
  },
  {
    name: 'Student Management',
    roles: ['ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION'],
    collapsible: true,
    items: [
      { name: 'Leads', href: '/dashboard/leads', icon: UserPlus, roles: ['ADMIN', 'MANAGER', 'RECEPTION'] },
      { name: 'Students', href: '/dashboard/students', icon: Users, roles: ['ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION', 'CASHIER'] },
      { name: 'Enrollments', href: '/dashboard/enrollments', icon: ClipboardList, roles: ['ADMIN', 'MANAGER', 'RECEPTION'] },
    ]
  },
  {
    name: 'Academic Management',
    roles: ['ADMIN', 'MANAGER', 'TEACHER'],
    collapsible: true,
    items: [
      { name: 'Teachers', href: '/dashboard/teachers', icon: UserCheck, roles: ['ADMIN', 'MANAGER'] },
      { name: 'Groups', href: '/dashboard/groups', icon: GraduationCap, roles: ['ADMIN', 'MANAGER', 'TEACHER'] },
      { name: 'Courses', href: '/dashboard/courses', icon: BookOpen, roles: ['ADMIN', 'MANAGER', 'TEACHER'] },
      { name: 'Classes', href: '/dashboard/classes', icon: Calendar, roles: ['ADMIN', 'MANAGER', 'TEACHER'] },
      { name: 'Attendance', href: '/dashboard/attendance', icon: UserCheck, roles: ['ADMIN', 'MANAGER', 'TEACHER'] },
      { name: 'Assessments', href: '/dashboard/assessments', icon: ClipboardCheck, roles: ['ADMIN', 'MANAGER', 'TEACHER'] }, // Test Administrator role only
    ]
  },
  {
    name: 'Financial Management',
    roles: ['ADMIN', 'CASHIER'], // Removed MANAGER from financial access
    collapsible: true,
    items: [
      { name: 'Payments', href: '/dashboard/payments', icon: CreditCard, roles: ['ADMIN', 'CASHIER'] },
      { name: 'Analytics', href: '/dashboard/analytics', icon: BarChart3, roles: ['ADMIN'] }, // ADMIN ONLY
    ]
  },
  {
    name: 'Student Progress',
    roles: ['STUDENT', 'PARENT'],
    items: [
      { name: 'My Progress', href: '/dashboard/student/progress', icon: TrendingUp, roles: ['STUDENT', 'PARENT'] },
      { name: 'My Schedule', href: '/dashboard/student/schedule', icon: Calendar, roles: ['STUDENT', 'PARENT'] },
      { name: 'My Payments', href: '/dashboard/student/payments', icon: CreditCard, roles: ['STUDENT', 'PARENT'] },
      { name: 'Certificates', href: '/dashboard/student/certificates', icon: Award, roles: ['STUDENT', 'PARENT'] },
      { name: 'Assignments', href: '/dashboard/student/assignments', icon: FileText, roles: ['STUDENT', 'PARENT'] },
    ]
  },
  {
    name: 'Communication',
    roles: ['ADMIN', 'MANAGER', 'TEACHER', 'STUDENT', 'PARENT'],
    items: [
      { name: 'Messages', href: '/dashboard/communication', icon: MessageSquare, roles: ['ADMIN', 'MANAGER', 'TEACHER', 'STUDENT', 'PARENT'] },
      { name: 'Announcements', href: '/dashboard/communication/announcements', icon: Phone, roles: ['ADMIN', 'MANAGER', 'TEACHER', 'STUDENT', 'PARENT'] },
    ]
  },
  {
    name: 'Administration',
    roles: ['ADMIN', 'MANAGER'],
    collapsible: true,
    items: [
      { name: 'Users', href: '/dashboard/users', icon: Shield, roles: ['ADMIN'] },
      { name: 'Activity Logs', href: '/dashboard/admin/activity-logs', icon: Activity, roles: ['ADMIN'] },
      { name: 'KPI Dashboard', href: '/dashboard/admin/kpis', icon: PieChart, roles: ['ADMIN', 'MANAGER'] },

      { name: 'Teacher KPIs', href: '/dashboard/admin/teacher-kpis', icon: TrendingUp, roles: ['ADMIN', 'MANAGER'] },
      { name: 'Settings', href: '/dashboard/settings', icon: Settings, roles: ['ADMIN', 'MANAGER'] },
    ]
  }
]

// Helper function to get student level color
const getStudentLevelColor = (level: string) => {
  const colors: { [key: string]: string } = {
    'A1': 'bg-red-100 text-red-800',
    'A2': 'bg-orange-100 text-orange-800',
    'B1': 'bg-yellow-100 text-yellow-800',
    'B2': 'bg-green-100 text-green-800',
    'C1': 'bg-blue-100 text-blue-800',
    'C2': 'bg-purple-100 text-purple-800',
    'IELTS_6_0': 'bg-indigo-100 text-indigo-800',
    'IELTS_6_5': 'bg-indigo-100 text-indigo-800',
    'IELTS_7_0': 'bg-indigo-100 text-indigo-800',
    'KIDS': 'bg-pink-100 text-pink-800',
  }
  return colors[level] || 'bg-gray-100 text-gray-800'
}

// Helper function to get next level in progression
const getNextLevel = (currentLevel: string): string | null => {
  const progression = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']
  const currentIndex = progression.indexOf(currentLevel)
  return currentIndex !== -1 && currentIndex < progression.length - 1
    ? progression[currentIndex + 1]
    : null
}

export function Sidebar() {
  const pathname = usePathname()
  const { data: session } = useSession()
  const [collapsedSections, setCollapsedSections] = useState<string[]>([])

  const userRole = session?.user?.role as string
  const userName = session?.user?.name

  // Mock student level - in real implementation, this would come from the session or API
  const studentLevel = userRole === 'STUDENT' ? 'B1' : null
  const nextLevel = studentLevel ? getNextLevel(studentLevel) : null

  // Filter navigation based on user role
  const getFilteredNavigation = () => {
    if (!userRole) return []

    return navigationConfig.filter(category =>
      category.roles.includes(userRole)
    ).map(category => ({
      ...category,
      items: category.items.filter(item =>
        item.roles.includes(userRole)
      )
    })).filter(category => category.items.length > 0)
  }

  const toggleSection = (sectionName: string) => {
    setCollapsedSections(prev =>
      prev.includes(sectionName)
        ? prev.filter(name => name !== sectionName)
        : [...prev, sectionName]
    )
  }

  const filteredNavigation = getFilteredNavigation()

  return (
    <div className="flex flex-col w-64 bg-white shadow-lg h-full">
      {/* Header */}
      <div className="flex items-center justify-center h-16 px-4 bg-blue-600">
        <BookOpen className="h-8 w-8 text-white mr-3" />
        <span className="text-xl font-bold text-white">Innovative CRM</span>
      </div>

      {/* User Info Section */}
      {session?.user && (
        <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                <span className="text-sm font-medium text-blue-600">
                  {userName?.charAt(0).toUpperCase()}
                </span>
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {userName}
              </p>
              <div className="flex items-center space-x-2">
                <p className="text-xs text-gray-500 capitalize">
                  {userRole?.toLowerCase()}
                </p>
                {studentLevel && (
                  <Badge className={cn('text-xs', getStudentLevelColor(studentLevel))}>
                    Level {studentLevel}
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Student Progress Indicator */}
          {studentLevel && nextLevel && (
            <div className="mt-2 p-2 bg-white rounded-md border">
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-600">Progress to {nextLevel}</span>
                <Target className="h-3 w-3 text-blue-500" />
              </div>
              <div className="mt-1 w-full bg-gray-200 rounded-full h-1.5">
                <div className="bg-blue-500 h-1.5 rounded-full" style={{ width: '65%' }}></div>
              </div>
              <p className="text-xs text-gray-500 mt-1">65% Complete</p>
            </div>
          )}
        </div>
      )}

      {/* Navigation */}
      <nav className="flex-1 px-4 py-4 space-y-1 overflow-y-auto">
        {filteredNavigation.map((category) => {
          const isCollapsed = collapsedSections.includes(category.name)
          const showItems = !category.collapsible || !isCollapsed

          return (
            <div key={category.name} className="space-y-1">
              {/* Category Header */}
              <div className="flex items-center justify-between">
                {category.collapsible ? (
                  <button
                    onClick={() => toggleSection(category.name)}
                    className="flex items-center w-full px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wider hover:text-gray-700 transition-colors"
                  >
                    <span className="flex-1 text-left">{category.name}</span>
                    {isCollapsed ? (
                      <ChevronRight className="h-3 w-3" />
                    ) : (
                      <ChevronDown className="h-3 w-3" />
                    )}
                  </button>
                ) : (
                  <h3 className="px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    {category.name}
                  </h3>
                )}
              </div>

              {/* Category Items */}
              {showItems && (
                <div className="space-y-1">
                  {category.items.map((item) => {
                    const isActive = pathname === item.href
                    return (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={cn(
                          'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                          isActive
                            ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                        )}
                      >
                        <item.icon className="mr-3 h-4 w-4 flex-shrink-0" />
                        <span className="truncate">{item.name}</span>
                      </Link>
                    )
                  })}
                </div>
              )}

              {/* Add spacing between categories */}
              {category !== filteredNavigation[filteredNavigation.length - 1] && (
                <div className="border-b border-gray-100 my-2"></div>
              )}
            </div>
          )
        })}
      </nav>
    </div>
  )
}
