import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import * as z from 'zod'

const updateClassSchema = z.object({
  teacherId: z.string().optional(),
  date: z.string().optional(),
  topic: z.string().optional(),
  homework: z.string().optional(),
  notes: z.string().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const classData = await prisma.class.findUnique({
      where: { id },
      include: {
        group: {
          include: {
            course: {
              select: {
                id: true,
                name: true,
                level: true,
                description: true,
                duration: true,
                price: true,
              },
            },
            enrollments: {
              where: { status: 'ACTIVE' },
              include: {
                student: {
                  include: {
                    user: {
                      select: {
                        id: true,
                        name: true,
                        phone: true,
                        email: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
        teacher: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                phone: true,
                email: true,
              },
            },
          },
        },
        attendances: {
          include: {
            student: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    phone: true,
                  },
                },
              },
            },
          },
          orderBy: {
            student: {
              user: {
                name: 'asc',
              },
            },
          },
        },
      },
    })

    if (!classData) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }

    // Calculate attendance statistics
    const totalStudents = classData.group.enrollments.length
    const presentStudents = classData.attendances.filter(att => att.status === 'PRESENT').length
    const lateStudents = classData.attendances.filter(att => att.status === 'LATE').length
    const absentStudents = classData.attendances.filter(att => att.status === 'ABSENT').length
    const excusedStudents = classData.attendances.filter(att => att.status === 'EXCUSED').length
    const attendanceRate = totalStudents > 0 ? (presentStudents / totalStudents) * 100 : 0

    // Get students who haven't been marked for attendance yet
    const markedStudentIds = classData.attendances.map(att => att.studentId)
    const unmarkedStudents = classData.group.enrollments.filter(
      enrollment => !markedStudentIds.includes(enrollment.studentId)
    )

    return NextResponse.json({
      ...classData,
      attendanceStats: {
        totalStudents,
        presentStudents,
        lateStudents,
        absentStudents,
        excusedStudents,
        attendanceRate: Math.round(attendanceRate * 100) / 100,
        unmarkedStudents: unmarkedStudents.length,
      },
      unmarkedStudents,
    })
  } catch (error) {
    console.error('Error fetching class:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const validatedData = updateClassSchema.parse(body)

    // Check if class exists
    const existingClass = await prisma.class.findUnique({
      where: { id },
    })

    if (!existingClass) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }

    // Validate teacher if provided
    if (validatedData.teacherId) {
      const teacher = await prisma.teacher.findUnique({
        where: { id: validatedData.teacherId },
      })
      if (!teacher) {
        return NextResponse.json(
          { error: 'Teacher not found' },
          { status: 400 }
        )
      }
    }

    // Check for date conflicts if date is being updated
    if (validatedData.date) {
      const conflictingClass = await prisma.class.findFirst({
        where: {
          groupId: existingClass.groupId,
          date: new Date(validatedData.date),
          id: { not: id }, // Exclude current class
        },
      })

      if (conflictingClass) {
        return NextResponse.json(
          { error: 'Another class already exists for this group on this date' },
          { status: 400 }
        )
      }
    }

    const updatedClass = await prisma.class.update({
      where: { id },
      data: {
        ...validatedData,
        date: validatedData.date ? new Date(validatedData.date) : undefined,
        updatedAt: new Date(),
      },
      include: {
        group: {
          include: {
            course: {
              select: {
                name: true,
                level: true,
              },
            },
          },
        },
        teacher: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        _count: {
          select: {
            attendances: true,
          },
        },
      },
    })

    return NextResponse.json(updatedClass)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating class:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    // Check if class exists
    const existingClass = await prisma.class.findUnique({
      where: { id },
      include: {
        attendances: true,
      },
    })

    if (!existingClass) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      )
    }

    // Check if class has attendance records
    if (existingClass.attendances.length > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete class with attendance records',
          details: `Class has ${existingClass.attendances.length} attendance record(s)`
        },
        { status: 400 }
      )
    }

    // Delete class
    await prisma.class.delete({
      where: { id },
    })

    return NextResponse.json({
      message: 'Class deleted successfully',
      deletedId: id
    })
  } catch (error) {
    console.error('Error deleting class:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
